<!--
 * 优惠券表格组件
 * 支持虚拟滚动、排序、筛选等功能
-->

<script setup lang="ts">
import type { CouponItem } from '@/api/modules/market/coupon'
import dayjs from 'dayjs'
import { computed, ref, watch } from 'vue'
import { COUPON_TYPE_OPTIONS, getCouponTypeOption, USE_ON_OPTIONS } from '../module/constants'

interface Props {
  data: CouponItem[]
  loading?: boolean
  total?: number
  currentPage?: number
  pageSize?: number
  tableHeight?: number | string
}

interface Emits {
  (e: 'selection-change', selection: CouponItem[]): void
  (e: 'row-dblclick', row: CouponItem): void
  (e: 'sort-change', { prop, order }: { prop: string, order: string }): void
  (e: 'size-change', size: number): void
  (e: 'current-change', page: number): void
  (e: 'status-change', row: CouponItem): void
  (e: 'detail', row: CouponItem): void
  (e: 'show-coupon-code', row: CouponItem): void
  (e: 'copy', row: CouponItem): void
  (e: 'edit-base', row: CouponItem): void
  (e: 'edit-config', row: CouponItem): void
  (e: 'delete', row: CouponItem): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  total: 0,
  currentPage: 1,
  pageSize: 10,
  tableHeight: 600,
})

const emit = defineEmits<Emits>()

const tableRef = ref()

// 计算显示的数据（用于虚拟滚动优化）
const displayData = computed(() => {
  // 如果数据量大，可以在这里实现虚拟滚动逻辑
  return props.data
})

// 工具函数
function getUseOnLabel(value: number) {
  return USE_ON_OPTIONS.find(item => item.value === value)?.label || '未知'
}

function getUseOnTagType(value: number) {
  const typeMap = {
    0: 'success', // 电影票
    1: 'primary', // 卖品
    2: 'warning', // 演出
    3: 'info', // 展览
  }
  return typeMap[value] || 'info'
}

function formatTimestamp(timestamp?: number) {
  if (!timestamp) { return '无效' }
  return dayjs.unix(timestamp).format('YYYY-MM-DD HH:mm')
}

// 事件处理
function handleSelectionChange(selection: CouponItem[]) {
  emit('selection-change', selection)
}

function handleRowDoubleClick(row: CouponItem) {
  emit('row-dblclick', row)
}

function handleSortChange({ prop, order }: { prop: string, order: string }) {
  emit('sort-change', { prop, order })
}

function handleSizeChange(size: number) {
  emit('size-change', size)
}

function handleCurrentChange(page: number) {
  emit('current-change', page)
}

function handleStatusChange(row: CouponItem) {
  emit('status-change', row)
}

function handleDetail(row: CouponItem) {
  emit('detail', row)
}

function handleShowCouponCode(row: CouponItem) {
  emit('show-coupon-code', row)
}

function handleCopy(row: CouponItem) {
  emit('copy', row)
}

function handleEditBase(row: CouponItem) {
  emit('edit-base', row)
}

function handleEditConfig(row: CouponItem) {
  emit('edit-config', row)
}

function handleDelete(row: CouponItem) {
  emit('delete', row)
}
</script>

<template>
  <div class="coupon-table">
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="displayData"
      size="small"
      :height="tableHeight"
      @selection-change="handleSelectionChange"
      @row-dblclick="handleRowDoubleClick"
      @sort-change="handleSortChange"
    >
      <el-table-column type="selection" width="55" />

      <el-table-column
        label="优惠券ID"
        prop="id"
        width="100"
        sortable="custom"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <el-text size="small">
            {{ row.id }}
          </el-text>
        </template>
      </el-table-column>

      <el-table-column
        label="优惠券名称"
        prop="name"
        width="160"
        sortable="custom"
        show-overflow-tooltip
      />

      <el-table-column label="适用商品" prop="useOn" width="120">
        <template #default="{ row }">
          <el-tag :type="getUseOnTagType(row.useOn)" size="small">
            {{ getUseOnLabel(row.useOn) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="券类型" prop="couponType" width="120">
        <template #default="{ row }">
          <el-tag :type="getCouponTypeOption(row.couponType)?.des?.type" size="small">
            {{ getCouponTypeOption(row.couponType)?.label }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="适用渠道" width="140">
        <template #default="{ row }">
          <el-tag v-if="row.channelRule?.channelScope === 2" size="small">
            全部渠道
          </el-tag>
          <el-popover v-else placement="bottom" trigger="hover" width="230">
            <el-card shadow="never">
              <template #header>
                <span>适用渠道：</span>
              </template>
              <el-tag
                v-for="(item, index) in row.channelRule?.channels"
                :key="index"
                size="small"
                style="margin: 2px;"
              >
                {{ item.channelName }}
              </el-tag>
            </el-card>
            <template #reference>
              <el-button link type="primary" size="small">
                包含渠道 {{ row.channelRule?.channels?.length || 0 }} 个
              </el-button>
            </template>
          </el-popover>
        </template>
      </el-table-column>

      <el-table-column label="生成方式" width="140">
        <template #default="{ row }">
          <span v-if="row.generateRule?.generateType">
            一次性生成
            <el-tag type="success" size="small">{{ row.generateRule.num }}</el-tag>
            张
          </span>
          <el-tag v-else type="warning" size="small">
            需要时生成
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        label="券绑定用户数"
        prop="bindCount"
        width="110"
        sortable="custom"
      />

      <el-table-column
        label="券已使用数"
        prop="usedCount"
        width="90"
        sortable="custom"
      />

      <el-table-column label="有效日期" width="180">
        <template #default="{ row }">
          <span v-if="row.periodRule?.validScope">
            绑定后
            <el-tag type="success" size="small">{{ row.periodRule.overdueDay }}</el-tag>
            天内有效
          </span>
          <span v-else>
            {{ formatTimestamp(row.periodRule?.startTime) }} 至
            {{ formatTimestamp(row.periodRule?.endTime) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="使用状态" width="120">
        <template #default="{ row }">
          <el-switch
            v-model="row.useStatus"
            size="small"
            inline-prompt
            active-text="开启"
            inactive-text="关闭"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(row)"
          />
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="160"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template #default="{ row }">
          <div class="flex gap-2">
            <el-button type="primary" link size="small" @click="handleDetail(row)">
              详情
            </el-button>
            <el-button type="primary" link size="small" @click="handleShowCouponCode(row)">
              查看券码
            </el-button>
            <el-dropdown trigger="click">
              <el-button type="primary" link size="small">
                更多
                <FaIcon name="arrow-down" />
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleCopy(row)">
                    <FaIcon name="copy" />
                    复制
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleEditBase(row)">
                    <FaIcon name="edit" />
                    修改主信息
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleEditConfig(row)">
                    <FaIcon name="edit" />
                    修改条件
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleDelete(row)">
                    <FaIcon name="delete" />
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      class="mt-4 text-right"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style scoped lang="scss">
.coupon-table {
  .flex {
    display: flex;
    align-items: center;
  }

  .gap-2 {
    gap: 8px;
  }
}
</style>

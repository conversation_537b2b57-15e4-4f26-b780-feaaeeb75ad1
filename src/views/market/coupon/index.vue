<script setup name="couponList">
// import { getCurrentInstance, reactive, ref, toRefs } from 'vue'
// import { listChannel } from '@/api/channel'
import {
  bindCoupon,
  changeCouponStatus,
  createCoupon,
  createCouponRestriction,
  getCouponList,
  getCouponRestriction
} from '@/api/modules/market/coupon'
// import { loadingHide, loadingShow } from '@/ui/components/FaLoading'
import { ElLoading } from 'element-plus'

// loadingShow({
//   type: 'plane',
//   size: 50,
//   text: '加载中...',
// })

// setTimeout(() => {
//   loadingHide()
// }, 2000)
import { USE_ON_OPTIONS, COUPON_TYPE_OPTIONS } from './module/constants'
import dayjs from 'dayjs'
import addCouponCom from './module/addCounponCom.vue'
// import couponCode from './module/couponCode.vue'
// import couponCodeQuery from './module/couponCodeQuery.vue'
import couponDetailCom from './module/couponDetail.vue'

const { proxy } = getCurrentInstance()
const aa = proxy.useDict('bus_film_type')
console.log(aa)

const couponList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const daterangeCreateTime = ref([])

let editCoupon = reactive(null) // 编辑券数据
// 渠道列表
const channelList = ref([])
// 显示隐藏券信息
const showCouponDialog = ref(false)
const showCouponDetail = ref(false)
const showCouponCode = ref(false)
const showCouponCodeQuery = ref(false)
let couponData = ref(null)

const data = reactive({
  form: {},
  queryParams: {
    id: '',
    page: 1, // 当前页码
    size: 10, // 每页条数
    name: undefined, // 优惠券名称
    useOn: '', // 适用类型 0,电影票 1,卖品 2.演出
    applyFilmType: '', // 适用影片版本
    applyCinema: undefined, // 适用影院
    couponType: '', // 优惠券类型
    validStartTime: undefined, // 有效开始时间
    validEndTime: undefined, // 有效结束时间
    useStatus: '', // 使用状态 0关闭，1开启
    generateType: '', // 生成数量 0：需要时生成 1：一次性生成
    isAsc: 'desc',
    orderByColumn: 'id',
  },
  rules: {
    testKey: [{ required: true, message: 'key键不能为空', trigger: 'blur' }],
    value: [{ required: true, message: '值不能为空', trigger: 'blur' }],
  },
})

const { queryParams } = toRefs(data)

/** 获取券列表 */
function getList() {
  loading.value = true

  getCouponList(
    queryParams.value
  ).then((response) => {
    const { code, data, msg } = response
    couponList.value = data.content
    total.value = data.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeCreateTime.value = []
  proxy.resetForm('queryRef')
  handleQuery()
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  editCoupon = null
  showCouponDialog.value = true
}

async function handleDetail(row, mode) {
  // loading
  // loading.value = true
  console.log({ row, mode })
  // let res = null
  // res = await getCouponRestriction({
  //   couponId: row.id,
  //   useOn: row.useOn,
  // })

  // console.log('详情', res)
  // loading.value = false
  // if (mode == 'view') {
  //   editCoupon = {
  //     mode: mode || 'view',
  //     data: res.data,
  //   }
  // }
  // couponData = res.data
  couponData.value = row
  showCouponDetail.value = true
}

async function handleEditBase(coupon, model) {
  console.log('修改主信息', { coupon }, { model })
  let res = null
  // res = await stepCouponMainDetail(coupon.id);
  // const { code, data, msg } = res;
  // console.log({ code, data, msg });

  editCoupon = {
    mode: model || 'view',
    step: 1,
    data: coupon,
  }
  showCouponDialog.value = true
}

async function handleEditConfig(coupon, model) {
  console.log('修改从信息', { coupon }, { model })
  editCoupon = {
    mode: model || 'view',
    step: 2,
    data: coupon,
  }
  showCouponDialog.value = true
}

function handleEdit(data) {
  handleDetail(data, 'edit')
}

// 启用禁用
function onCouponUseStatusChange(row) {
  // couponStatusToggle({ id: row.id, useStatus: row.useStatus.code }).then(
  //   (res) => {
  //     proxy.$modal.msgSuccess("操作成功");
  //     getList();
  //   }
  // );
  console.log('启用禁用', row)
  changeCouponStatus({ id: row.id, useStatus: row.couponCodeCreateConfig.useStatus }).then(
    (res) => {
      proxy.$modal.msgSuccess('操作成功')
      getList()
    },
  ).catch(() => {
    // row.couponCodeCreateConfig.useStatus = !row.couponCodeCreateConfig.useStatus
    getList()
  })
}

/** 修改按钮操作 */
function handleCopy(row) {
  proxy.$modal
    .confirm('确认复制优惠券？')
    .then(() => {
      loading.value = true
      // console.log(row);
      // if (row.useOn.name == "GOODS") {
      //   return goodsCouponCopy({ id: row.id });
      // } else if (row.useOn.name == "FILM") {
      //   return couponCopy({ id: row.id });
      // } else if (row.useOn.name == "SHOW") {
      //   return showCouponCopy({ id: row.id });
      // }
      stepCouponInfoCopy(row.id).then((res) => {
        loading.value = false
        getList()
        proxy.$modal.msgSuccess('已复制')
      })
    })
  // .then(() => {
  //   loadingHide;
  //   getList();
  //   proxy.$modal.msgSuccess("已复制");
  // })
  // .finally(() => {
  //   loadingHide;
  // });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const ids = row.id || ids.value
  proxy.$modal
    .confirm('确认删除该优惠券吗?')
    .then(() => {
      loading.value = true
      stepCouponInfoDel(ids).then((res) => {
        loading.value = false
        getList()
        proxy.$modal.msgSuccess('删除成功')
      }).catch(() => {
        loading.value = false
      })
      // // return couponDel(ids);
      // if (row.useOn.name == "GOODS") {
      //   return goodsCouponDel(ids);
      // } else if (row.useOn.name == "FILM") {
      //   return couponDel(ids);
      // } else if (row.useOn.name == "SHOW") {
      //   return showCouponDel(ids);
      // }
    })
  // .then(() => {
  //   loadingHide;
  //   getList();
  //   proxy.$modal.msgSuccess("删除成功");
  // })
  // .finally(() => {
  //   loadingHide;
  // });
}

// 获取渠道
function getListChannel() {
  // listChannel({ page: 1, pageSize: 30 }).then((res) => {
  //   let { rows = [] } = res
  //   channelList.value = rows.map((item) => {
  //     return { id: item.id, label: item.channelName }
  //   })
  // })
  channelList.value = [
    { id: 0, label: '全部' },
    { id: 1, label: '抖音' },
    { id: 2, label: '快手' },
    { id: 3, label: '微信' },
    { id: 4, label: '抖音' },
  ]
}
function dataFormat(temp) {
  if (!temp) {
    return '无效'
  }
  return dayjs.unix(temp).format('YYYY-MM-DD HH:mm')
}

getListChannel()

getList()

function handleShowCouponCode(data) {
  couponData.value = data
  showCouponCode.value = true
}

function handleCouponCodeQuery() {
  showCouponCodeQuery.value = true
}
function handleSizeChange(size) {
  queryParams.size = size
  getList()
}
function handleCurrentChange(page) {
  queryParams.page = page
  getList()
}

const FINDCOUPON_TYPE_OPTIONS = function (typeNum) {

  return COUPON_TYPE_OPTIONS.find(item => item.value === typeNum)
}
</script>

<template>
  <div>
    <fa-page-header title="优惠券" description="优惠券列表管理" />
    <!-- <el-loading :loading="loading" /> -->
    <fa-page-main>
      <el-form v-show="showSearch" ref="queryRef" :model="queryParams" size="small" :inline="true">
        <el-form-item label="优惠券ID" aria-label="优惠券ID" prop="id">
          <el-input v-model="queryParams.id" placeholder="请输入优惠券ID" clearable aria-label="优惠券ID" style="width: 200px;"
            @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="优惠券名称" aria-label="优惠券名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入优惠券名称" clearable aria-label="优惠券名称" style="width: 200px;"
            @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="适用类型" prop="useOn">
          <el-select v-model="queryParams.useOn" placeholder="请选择适用类型" clearable style="width: 200px;">
            <!-- <el-option label="电影票" aria-label="电影票" :value="0" /> -->
            <el-option aria-label="卖品" :value="1" v-for="item in USE_ON_OPTIONS" :key="item.value"
              :label="item.label" />
            <!-- <el-option label="演出" aria-label="演出" :value="2" /> -->
            <!-- <el-option label="展览" aria-label="展览" :value="3" /> -->
          </el-select>
        </el-form-item>
        <el-form-item label="渠道" prop="channelId">
          <el-select v-model="queryParams.channelId" placeholder="请选择渠道" style="width: 200px;">
            <el-option v-for="item in channelList" :key="item.id" :label="item.label" aria-label="电影票"
              :value="item.id" />
          </el-select>
        </el-form-item>
        <!--
      <el-form-item label="影片版本" aria-label="影片版本" prop="applyFilmType">
        <el-select v-model="queryParams.applyFilmType" clearable placeholder="请选择影片版本" style="width: 200px">
          <el-option label="全部" value="" />
          <el-option v-for="dict in bus_film_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item> -->
        <el-form-item label="优惠券类型" prop="couponType">
          <el-select v-model="queryParams.couponType" placeholder="请选择优惠券类型" style="width: 200px;">
            <el-option v-for="item in COUPON_TYPE_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="生成方式" prop="generateType">
          <el-select v-model="queryParams.generateType" placeholder="请选择生成方式" style="width: 200px;" clearable>
            <el-option label="全部" value="" />
            <el-option label="一次性生成" value="1" />
            <el-option label="需要时生成" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="使用状态" prop="useStatus">
          <el-select v-model="queryParams.useStatus" placeholder="请选择使用状态" style="width: 200px;">
            <el-option label="全部" value="" />
            <el-option label="开启" :value="1" />
            <el-option label="关闭" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="有效日期">
          <el-date-picker v-model="daterangeCreateTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
            range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 1, 1, 23, 59, 59),
            ]" style="width: 260px;" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <template #icon>
              <FaIcon name="i-ep:search" />
            </template>
            查询
          </el-button>
          <el-button @click="resetQuery">
            <template #icon>
              <FaIcon name="i-ep:refresh" />
            </template>
            重置
          </el-button>
          <el-button type="success" @click="handleCouponCodeQuery">
            <template #icon>
              <FaIcon name="i-ep:search" />
            </template>
            查询券码
          </el-button>
          <el-button type="primary" @click="handleAdd">
            <template #icon>
              <FaIcon name="i-ep:plus" />
            </template>
            新增券
          </el-button>
        </el-form-item>
      </el-form>
      <!-- <pre>
  {{ couponList }}
</pre> -->
      <el-table v-loading="loading" :data="couponList" size="small" @selection-change="handleSelectionChange"
        @row-dblclick="handleDetail">
        <el-table-column label="优惠券ID" aria-label="优惠券ID" align="center" prop="id" width="100">
          <template #default="scope">
            <el-text size="small">
              {{ scope.row.id }}
            </el-text>
          </template>
        </el-table-column>
        <el-table-column label="优惠券名称" align="center" prop="name" width="160" />
        <el-table-column label="适用商品" align="center" prop="useOn">
          <template #default="scope">
            <el-tag v-if="scope.row.useOn === 0" type="success" round>
              电影票
            </el-tag>
            <el-tag v-if="scope.row.useOn === 1" type="primary" round>
              卖品
            </el-tag>
            <el-tag v-if="scope.row.useOn === 2" type="warning" round>
              演出
            </el-tag>
            <el-tag v-if="scope.row.useOn === 3" type="info" round>
              展览
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="券类型" align="center" prop="couponType">
          <template #default="scope">
            <el-tag :type="FINDCOUPON_TYPE_OPTIONS(scope.row.couponType)?.des?.type">
              {{ FINDCOUPON_TYPE_OPTIONS(scope.row.couponType)?.label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="适用渠道" align="center" prop="cinemaScope" width="140">
          <template #default="{ row }">
            <el-tag v-if="row.channelRule.channelScope === 2">全部渠道</el-tag>
            <el-popover placement="bottom" trigger="hover" width="230" v-else>
              <el-card shadow="never">
                <template #header>
                  <span>适用渠道：</span>
                </template>
                <el-tag v-for="(item, index) in row?.channelRule?.channels" :key="index" style="margin: 2px;"
                  title="包含渠道">
                  {{ item.channelName }}
                </el-tag>
              </el-card>

              <template #reference>
                <!-- {{ row.couponChannels }} -->
                <span>包含渠道<el-text type="primary">{{ row?.channelRule?.channels?.length || 0 }}</el-text>个</span>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="适用影院" align="center" prop="cinemaScope" width="140">
          <template #default="{ row }">
            <div v-if="row.couponRuleCinemas?.length">
              <!-- {{ row.couponRuleCinemas }} -->
              <el-popover placement="bottom" :title="row.cinemaScopeText" :width="400" trigger="hover">
                <el-card v-for="(item, index) in row.couponTicketConfigs" :key="index" shadow="never"
                  style="margin-bottom: 10px;">
                  <template #header>
                    <!--                  {{item.couponTicketSelectScopeConfig}} -->
                    <el-text v-if="item.couponTicketSelectScopeConfig.cinemaScope === 0" type="primary">
                      规则{{ index + 1 }} 全部影院
                    </el-text>
                    <el-text v-else-if="item.couponTicketSelectScopeConfig.cinemaScope === 1" type="success">
                      规则{{ index + 1 }} 指定影院
                    </el-text>
                    <el-text v-else type="danger">
                      规则{{ index + 1 }} 排除影院
                    </el-text>
                  </template>

                  <div v-if="item.couponTicketSelectScopeConfig.cinemaScope === 1">
                    <el-tag
                      v-for="(cinema, index) in row.couponRuleCinemas.find(item => item.ruleId === row.couponTicketConfigs[index].couponTicketSelectScopeConfig.ruleId).cinemaNames || []"
                      :key="index" type="success" size="small" style="margin: 2px;">
                      <span>{{ cinema }}</span>
                    </el-tag>
                  </div>
                  <div v-else-if="item.couponTicketSelectScopeConfig.cinemaScope === 2">
                    <el-tag
                      v-for="(cinema, index) in row.couponRuleCinemas.find(item => item.ruleId === row.couponTicketConfigs[index].couponTicketSelectScopeConfig.ruleId).cinemaNames || []"
                      :key="index" type="danger" size="small" style="margin: 2px;">
                      <span>{{ cinema }}</span>
                    </el-tag>
                  </div>

                  <!--                指定影院{{ item.cinemaNames }}家 -->
                </el-card>
                <template #reference>
                  <el-button link type="primary" size="small">
                    {{ row.couponTicketConfigs?.length || 0 }} 组规则
                  </el-button>
                </template>
              </el-popover>
            </div>
            <div v-else>
              不限影院
            </div>
          </template>
        </el-table-column>
        <el-table-column label="生成方式" align="center" prop="num" width="140">
          <template #default="{ row }">
            <span v-if="row.generateRule.generateType">一次性生成
              <el-tag type="success">{{ row.generateRule.num }} </el-tag>
              张</span>
            <span v-else>
              <el-tag type="warning">需要时生成</el-tag>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="券绑定用户数" align="center" prop="bindCount" width="110" />
        <el-table-column label="券已使用数" align="center" prop="useCount" width="90" />
        <el-table-column label="有效日期" align="center" width="180">
          <template #default="{ row }">
            <span v-if="row.periodRule.validScope">
              绑定后 <el-tag type="success">{{ row.periodRule.overdueDay }}</el-tag> 天内有效
            </span>

            <span v-else>{{ dataFormat(row.periodRule.startTime) }} 至 {{ dataFormat(row.periodRule.endTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="使用状态" align="center" width="180">
          <template #default="{ row }">
            <!-- {{ row.couponCodeCreateConfig.useStatus }} -->
            <el-switch v-model="row.useStatus" size="large" inline-prompt active-text="开启" inactive-text="关闭"
              :active-value="1" :inactive-value="0" @change="onCouponUseStatusChange(row)" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="160" fixed="right" class-name="small-padding fixed-width">
          <template #default="{ row }">
            <div class="flex">
              <el-button type="primary" link size="small" @click="handleDetail(row, 'view')">
                详情
              </el-button>
              <el-button type="primary" class="marr8" link size="small" @click="handleShowCouponCode(row)">
                查看券码
              </el-button>
              <!-- <el-popconfirm
                title="确认复制优惠券？"
                @confirm="handleCopy(row)">
                <template #reference>
                    <el-button type="primary" link>复制</el-button>
                </template>
    </el-popconfirm> -->
              <el-dropdown trigger="click">
                <el-button type="primary" link size="small">
                  更多
                  <!-- <el-icon class="el-icon--right">
                    <arrow-down />
                  </el-icon> -->
                  <FaIcon name="arrow-down" />
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item icon="CopyDocument" @click="handleCopy(row)">
                      <FaIcon name="copy" />
                      复制
                    </el-dropdown-item>
                    <!-- <el-dropdown-item v-if="row.useStatus.code == '0'" @click="handleDetail(row, 'edit')"
                    icon="Edit">修改</el-dropdown-item> -->
                    <!-- 编辑主信息 -->
                    <el-dropdown-item style="color: #2ecc71;" @click="handleEditBase(row, 'edit')">
                      <FaIcon name="edit" />
                      修改主信息
                    </el-dropdown-item>
                    <!-- 编辑从配置 -->
                    <el-dropdown-item style="color: #9b59b6;" @click="handleEditConfig(row, 'edit')">
                      <FaIcon name="edit" />
                      修改条件
                    </el-dropdown-item>
                    <el-dropdown-item style="color: red;" @click="handleDelete(row)">
                      <FaIcon name="delete" />
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- <pagination v-show="total > 0" v-model:page="queryParams.page" v-model:limit="queryParams.pageSize" :total="total"
        @pagination="getList" />
    </fa-page-main> -->
      <el-pagination v-model:current-page="queryParams.page" v-model:page-size="queryParams.size" :total="total"
        :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" class="mt-4 text-right"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </fa-page-main>

    <!-- 新增、修改券 -->
    <add-coupon-com v-if="showCouponDialog" :model-value="showCouponDialog" :edit-coupon="editCoupon"
      @update:model-value="showCouponDialog = $event" @refresh="getList" @close="showCouponDialog = false" />

      <!-- 券详情 -->
    <coupon-detail-com
      v-if="showCouponDetail"  :coupon-data="couponData"
      @close="showCouponDetail = false"
    />

    <!-- 券码 -->
    <!-- <coupon-code
      v-if="showCouponCode" :coupon-data="couponData" :model-value="showCouponCode"
      @update:model-value="showCouponCode = $event" @refresh="getList" @close="showCouponCode = false"
    /> -->

    <!-- 券码查询结果 -->
    <!-- <coupon-code-query
      v-if="showCouponCodeQuery" :model-value="showCouponCodeQuery"
      :channel-list="channelList" @close="showCouponCodeQuery = false"
    /> -->
  </div>
</template>

<style scoped lang="scss">
.flex {
  display: flex;
}

.marr8 {
  margin-right: 8px;
}
</style>

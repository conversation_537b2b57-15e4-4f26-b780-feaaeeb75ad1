<script setup name="couponList">
import { onMounted, ref } from 'vue'
import CouponSearchForm from './components/CouponSearchForm.vue'
import CouponTable from './components/CouponTable.vue'
import { useCouponList } from './composables/useCouponList'
import addCouponCom from './module/addCounponCom.vue'
import couponDetailCom from './module/couponDetail.vue'

const { proxy } = getCurrentInstance()

// 使用优化的列表管理
const {
  couponList,
  loading,
  total,
  queryParams,
  fetchCouponList,
  handleSearch,
  handleReset,
  handleSizeChange,
  handleCurrentChange,
  handleSelectionChange,
  handleStatusChange,
} = useCouponList()

// 其他响应式数据
const showSearch = ref(true)

// 编辑相关状态
const editCouponData = ref(null)

// 弹窗显示状态
const showCouponDialog = ref(false)
const showCouponDetail = ref(false)
const showCouponCode = ref(false)
const showCouponCodeQuery = ref(false)
const currentCouponData = ref(null)

// ==================== 业务逻辑相关 ====================

// ==================== 操作相关 ====================

/** 新增优惠券 */
function handleAdd() {
  editCouponData.value = null
  showCouponDialog.value = true
}

/** 查看优惠券详情 */
async function handleDetail(row, mode = 'view') {
  try {
    console.log('查看详情:', { row, mode })
    currentCouponData.value = row
    showCouponDetail.value = true
  }
  catch (error) {
    console.error('查看详情失败:', error)
    proxy.$modal.msgError('查看详情失败')
  }
}

/** 编辑优惠券基本信息 */
async function handleEditBase(coupon, mode = 'edit') {
  try {
    console.log('编辑基本信息:', { coupon, mode })
    editCouponData.value = {
      mode,
      step: 1,
      data: coupon,
    }
    showCouponDialog.value = true
  }
  catch (error) {
    console.error('编辑基本信息失败:', error)
    proxy.$modal.msgError('编辑基本信息失败')
  }
}

/** 编辑优惠券配置信息 */
async function handleEditConfig(coupon, mode = 'edit') {
  try {
    console.log('编辑配置信息:', { coupon, mode })
    editCouponData.value = {
      mode,
      step: 2,
      data: coupon,
    }
    showCouponDialog.value = true
  }
  catch (error) {
    console.error('编辑配置信息失败:', error)
    proxy.$modal.msgError('编辑配置信息失败')
  }
}

/** 复制优惠券 */
async function handleCopy(_row) {
  try {
    await proxy.$modal.confirm('确认复制优惠券？')
    loading.value = true

    // TODO: 实现复制功能
    // await stepCouponInfoCopy(row.id)

    proxy.$modal.msgSuccess('已复制')
    fetchCouponList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('复制失败:', error)
      proxy.$modal.msgError('复制失败')
    }
  }
  finally {
    loading.value = false
  }
}

/** 删除优惠券 */
async function handleDelete(row) {
  try {
    await proxy.$modal.confirm('确认删除该优惠券吗?')
    loading.value = true

    // TODO: 实现删除功能
    // const deleteId = row.id || selectedIds.value
    // await stepCouponInfoDel(deleteId)
    console.log('删除优惠券:', row.id)

    proxy.$modal.msgSuccess('删除成功')
    fetchCouponList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      proxy.$modal.msgError('删除失败')
    }
  }
  finally {
    loading.value = false
  }
}

// ==================== 工具函数 ====================

/** 获取渠道列表 */
function fetchChannelList() {
  // TODO: 替换为真实的API调用
  channelList.value = [
    { id: 0, label: '全部' },
    { id: 1, label: '抖音' },
    { id: 2, label: '快手' },
    { id: 3, label: '微信' },
    { id: 4, label: '小程序' },
  ]
}

/** 格式化时间戳 */
function formatTimestamp(timestamp) {
  if (!timestamp) {
    return '无效'
  }
  return dayjs.unix(timestamp).format('YYYY-MM-DD HH:mm')
}

/** 查找优惠券类型选项 */
function findCouponTypeOption(typeNum) {
  return COUPON_TYPE_OPTIONS.find(item => item.value === typeNum)
}

// ==================== 弹窗相关 ====================

/** 显示券码弹窗 */
function handleShowCouponCode(data) {
  currentCouponData.value = data
  showCouponCode.value = true
}

/** 显示券码查询弹窗 */
function handleShowCouponCodeQuery() {
  showCouponCodeQuery.value = true
}

// ==================== 初始化 ====================

// 初始化数据
onMounted(() => {
  fetchChannelList()
  fetchCouponList()
})
</script>

<template>
  <div class="coupon-page">
    <fa-page-header title="优惠券" description="优惠券列表管理" />

    <fa-page-main>
      <!-- 搜索表单 -->
      <CouponSearchForm
        v-show="showSearch"
        v-model="queryParams"
        :loading="loading"
        @search="handleSearch"
        @reset="handleReset"
        @coupon-code-query="handleShowCouponCodeQuery"
        @add="handleAdd"
      />

      <!-- 优惠券表格 -->
      <CouponTable
        :data="couponList"
        :loading="loading"
        :total="total"
        :current-page="queryParams.page"
        :page-size="queryParams.size"
        @selection-change="handleSelectionChange"
        @row-dblclick="handleDetail"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        @status-change="handleStatusChange"
        @detail="handleDetail"
        @show-coupon-code="handleShowCouponCode"
        @copy="handleCopy"
        @edit-base="handleEditBase"
        @edit-config="handleEditConfig"
        @delete="handleDelete"
      />
    </fa-page-main>

    <!-- 新增、修改券 -->
    <add-coupon-com
      v-if="showCouponDialog"
      :model-value="showCouponDialog"
      :edit-coupon="editCouponData"
      @update:model-value="showCouponDialog = $event"
      @refresh="fetchCouponList"
      @close="showCouponDialog = false"
    />

    <!-- 券详情 -->
    <coupon-detail-com
      v-if="showCouponDetail"
      :coupon-data="currentCouponData"
      @close="showCouponDetail = false"
    />
  </div>
</template>

<style scoped lang="scss">
.coupon-page {
  .flex {
    display: flex;
    gap: 8px;
    align-items: center;
  }
}
</style>

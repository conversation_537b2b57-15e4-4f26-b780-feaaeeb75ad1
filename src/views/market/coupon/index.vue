<script setup name="couponList">
import { onMounted, ref } from 'vue'
import CouponSearchForm from './components/CouponSearchForm.vue'
import CouponTable from './components/CouponTable.vue'
import { useCouponList } from './composables/useCouponList'
import addCouponCom from './module/addCounponCom.vue'
import couponDetailCom from './module/couponDetail.vue'

const { proxy } = getCurrentInstance()

// 使用优化的列表管理
const {
  couponList,
  loading,
  total,
  queryParams,
  fetchCouponList,
  handleSearch,
  handleReset,
  handleSizeChange,
  handleCurrentChange,
  handleSelectionChange,
  handleStatusChange,
} = useCouponList()

// 其他响应式数据
const showSearch = ref(true)

// 编辑相关状态
const editCouponData = ref(null)

// 弹窗显示状态
const showCouponDialog = ref(false)
const showCouponDetail = ref(false)
const showCouponCode = ref(false)
const showCouponCodeQuery = ref(false)
const currentCouponData = ref(null)

// ==================== 业务逻辑相关 ====================

// ==================== 操作相关 ====================

/** 新增优惠券 */
function handleAdd() {
  editCouponData.value = null
  showCouponDialog.value = true
}

/** 查看优惠券详情 */
async function handleDetail(row, mode = 'view') {
  try {
    console.log('查看详情:', { row, mode })
    currentCouponData.value = row
    showCouponDetail.value = true
  }
  catch (error) {
    console.error('查看详情失败:', error)
    proxy.$modal.msgError('查看详情失败')
  }
}

/** 编辑优惠券基本信息 */
async function handleEditBase(coupon, mode = 'edit') {
  try {
    console.log('编辑基本信息:', { coupon, mode })
    editCouponData.value = {
      mode,
      step: 1,
      data: coupon,
    }
    showCouponDialog.value = true
  }
  catch (error) {
    console.error('编辑基本信息失败:', error)
    proxy.$modal.msgError('编辑基本信息失败')
  }
}

/** 编辑优惠券配置信息 */
async function handleEditConfig(coupon, mode = 'edit') {
  try {
    console.log('编辑配置信息:', { coupon, mode })
    editCouponData.value = {
      mode,
      step: 2,
      data: coupon,
    }
    showCouponDialog.value = true
  }
  catch (error) {
    console.error('编辑配置信息失败:', error)
    proxy.$modal.msgError('编辑配置信息失败')
  }
}

/** 复制优惠券 */
async function handleCopy(_row) {
  try {
    await proxy.$modal.confirm('确认复制优惠券？')
    loading.value = true

    // TODO: 实现复制功能
    // await stepCouponInfoCopy(row.id)

    proxy.$modal.msgSuccess('已复制')
    fetchCouponList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('复制失败:', error)
      proxy.$modal.msgError('复制失败')
    }
  }
  finally {
    loading.value = false
  }
}

/** 删除优惠券 */
async function handleDelete(row) {
  try {
    await proxy.$modal.confirm('确认删除该优惠券吗?')
    loading.value = true

    // TODO: 实现删除功能
    // const deleteId = row.id || selectedIds.value
    // await stepCouponInfoDel(deleteId)
    console.log('删除优惠券:', row.id)

    proxy.$modal.msgSuccess('删除成功')
    fetchCouponList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      proxy.$modal.msgError('删除失败')
    }
  }
  finally {
    loading.value = false
  }
}

// ==================== 工具函数 ====================

/** 获取渠道列表 */
function fetchChannelList() {
  // TODO: 替换为真实的API调用
  channelList.value = [
    { id: 0, label: '全部' },
    { id: 1, label: '抖音' },
    { id: 2, label: '快手' },
    { id: 3, label: '微信' },
    { id: 4, label: '小程序' },
  ]
}

/** 格式化时间戳 */
function formatTimestamp(timestamp) {
  if (!timestamp) {
    return '无效'
  }
  return dayjs.unix(timestamp).format('YYYY-MM-DD HH:mm')
}

/** 查找优惠券类型选项 */
function findCouponTypeOption(typeNum) {
  return COUPON_TYPE_OPTIONS.find(item => item.value === typeNum)
}

// ==================== 弹窗相关 ====================

/** 显示券码弹窗 */
function handleShowCouponCode(data) {
  currentCouponData.value = data
  showCouponCode.value = true
}

/** 显示券码查询弹窗 */
function handleShowCouponCodeQuery() {
  showCouponCodeQuery.value = true
}

// ==================== 初始化 ====================

// 初始化数据
onMounted(() => {
  fetchChannelList()
  fetchCouponList()
})
</script>

<template>
  <div class="coupon-page">
    <fa-page-header title="优惠券" description="优惠券列表管理" />

    <fa-page-main>
      <!-- 搜索表单 -->
      <CouponSearchForm
        v-show="showSearch"
        v-model="queryParams"
        :loading="loading"
        @search="handleSearch"
        @reset="handleReset"
        @coupon-code-query="handleShowCouponCodeQuery"
        @add="handleAdd"
      />

      <!-- 优惠券表格 -->
      <CouponTable
        :data="couponList"
        :loading="loading"
        :total="total"
        :current-page="queryParams.page"
        :page-size="queryParams.size"
        @selection-change="handleSelectionChange"
        @row-dblclick="handleDetail"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        @status-change="handleStatusChange"
        @detail="handleDetail"
        @show-coupon-code="handleShowCouponCode"
        @copy="handleCopy"
        @edit-base="handleEditBase"
        @edit-config="handleEditConfig"
        @delete="handleDelete"
      />
    </fa-page-main>

    <!-- 新增、修改券 -->
    <add-coupon-com
      v-if="showCouponDialog"
      :model-value="showCouponDialog"
      :edit-coupon="editCouponData"
      @update:model-value="showCouponDialog = $event"
      @refresh="fetchCouponList"
      @close="showCouponDialog = false"
    />

    <!-- 券详情 -->
    <coupon-detail-com
      v-if="showCouponDetail"
      :coupon-data="currentCouponData"
      @close="showCouponDetail = false"
    />
  </div>
</template>

<style scoped lang="scss">
.coupon-page {
  .flex {
    display: flex;
    gap: 8px;
    align-items: center;
  }
}
</style>

        <el-option
          v-for="item in channelList" :key="item.id" :label="item.label" aria-label="电影票"
          :value="item.id"
        />
      </el-select>
      </el-form-item>

      <!--
      <el-form-item label="影片版本" aria-label="影片版本" prop="applyFilmType">
        <el-select v-model="queryParams.applyFilmType" clearable placeholder="请选择影片版本" style="width: 200px">
          <el-option label="全部" value="" />
          <el-option v-for="dict in bus_film_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="优惠券类型" prop="couponType">
        <el-select v-model="queryParams.couponType" placeholder="请选择优惠券类型" style="width: 200px;">
          <el-option v-for="item in COUPON_TYPE_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="生成方式" prop="generateType">
        <el-select v-model="queryParams.generateType" placeholder="请选择生成方式" style="width: 200px;" clearable>
          <el-option label="全部" value="" />
          <el-option label="一次性生成" value="1" />
          <el-option label="需要时生成" value="0" />
        </el-select>
      </el-form-item>

      <el-form-item label="使用状态" prop="useStatus">
        <el-select v-model="queryParams.useStatus" placeholder="请选择使用状态" style="width: 200px;">
          <el-option label="全部" value="" />
          <el-option label="开启" :value="1" />
          <el-option label="关闭" :value="0" />
        </el-select>
      </el-form-item>

      <el-form-item label="有效日期">
        <el-date-picker
          v-model="daterangeCreateTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="[
            new Date(2000, 1, 1, 0, 0, 0),
            new Date(2000, 1, 1, 23, 59, 59),
          ]" style="width: 260px;"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <template #icon>
            <FaIcon name="i-ep:search" />
          </template>
          查询
        </el-button>
        <el-button @click="handleReset">
          <template #icon>
            <FaIcon name="i-ep:refresh" />
          </template>
          重置
        </el-button>
        <el-button type="success" @click="handleShowCouponCodeQuery">
          <template #icon>
            <FaIcon name="i-ep:search" />
          </template>
          查询券码
        </el-button>
        <el-button type="primary" @click="handleAdd">
          <template #icon>
            <FaIcon name="i-ep:plus" />
          </template>
          新增券
        </el-button>
      </el-form-item>

      </el-form>
      <!-- <pre>
  {{ couponList }}
</pre> -->
      <el-table
        v-loading="loading" :data="couponList" size="small" @selection-change="handleSelectionChange"
        @row-dblclick="handleDetail"
      >
        <el-table-column label="优惠券ID" aria-label="优惠券ID" align="center" prop="id" width="100">
          <template #default="scope">
            <el-text size="small">
              {{ scope.row.id }}
            </el-text>
          </template>
        </el-table-column>
        <el-table-column label="优惠券名称" align="center" prop="name" width="160" />
        <el-table-column label="适用商品" align="center" prop="useOn">
          <template #default="scope">
            <el-tag v-if="scope.row.useOn === 0" type="success" round>
              电影票
            </el-tag>
            <el-tag v-if="scope.row.useOn === 1" type="primary" round>
              卖品
            </el-tag>
            <el-tag v-if="scope.row.useOn === 2" type="warning" round>
              演出
            </el-tag>
            <el-tag v-if="scope.row.useOn === 3" type="info" round>
              展览
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="券类型" align="center" prop="couponType">
          <template #default="scope">
            <el-tag :type="findCouponTypeOption(scope.row.couponType)?.des?.type">
              {{ findCouponTypeOption(scope.row.couponType)?.label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="适用渠道" align="center" prop="cinemaScope" width="140">
          <template #default="{ row }">
            <el-tag v-if="row.channelRule.channelScope === 2">
              全部渠道
            </el-tag>
            <el-popover v-else placement="bottom" trigger="hover" width="230">
              <el-card shadow="never">
                <template #header>
                  <span>适用渠道：</span>
                </template>
                <el-tag
                  v-for="(item, index) in row?.channelRule?.channels" :key="index" style="margin: 2px;"
                  title="包含渠道"
                >
                  {{ item.channelName }}
                </el-tag>
              </el-card>

              <template #reference>
                <!-- {{ row.couponChannels }} -->
                <span>包含渠道<el-text type="primary">{{ row?.channelRule?.channels?.length || 0 }}</el-text>个</span>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="适用影院" align="center" prop="cinemaScope" width="140">
          <template #default="{ row }">
            <div v-if="row.couponRuleCinemas?.length">
              <!-- {{ row.couponRuleCinemas }} -->
              <el-popover placement="bottom" :title="row.cinemaScopeText" :width="400" trigger="hover">
                <el-card
                  v-for="(item, index) in row.couponTicketConfigs" :key="index" shadow="never"
                  style="margin-bottom: 10px;"
                >
                  <template #header>
                    <!--                  {{item.couponTicketSelectScopeConfig}} -->
                    <el-text v-if="item.couponTicketSelectScopeConfig.cinemaScope === 0" type="primary">
                      规则{{ index + 1 }} 全部影院
                    </el-text>
                    <el-text v-else-if="item.couponTicketSelectScopeConfig.cinemaScope === 1" type="success">
                      规则{{ index + 1 }} 指定影院
                    </el-text>
                    <el-text v-else type="danger">
                      规则{{ index + 1 }} 排除影院
                    </el-text>
                  </template>

                  <div v-if="item.couponTicketSelectScopeConfig.cinemaScope === 1">
                    <el-tag
                      v-for="(cinema, cinemaIndex) in row.couponRuleCinemas.find(rule => rule.ruleId === row.couponTicketConfigs[index].couponTicketSelectScopeConfig.ruleId).cinemaNames || []"
                      :key="cinemaIndex" type="success" size="small" style="margin: 2px;"
                    >
                      <span>{{ cinema }}</span>
                    </el-tag>
                  </div>
                  <div v-else-if="item.couponTicketSelectScopeConfig.cinemaScope === 2">
                    <el-tag
                      v-for="(cinema, cinemaIndex) in row.couponRuleCinemas.find(rule => rule.ruleId === row.couponTicketConfigs[index].couponTicketSelectScopeConfig.ruleId).cinemaNames || []"
                      :key="cinemaIndex" type="danger" size="small" style="margin: 2px;"
                    >
                      <span>{{ cinema }}</span>
                    </el-tag>
                  </div>

                  <!--                指定影院{{ item.cinemaNames }}家 -->
                </el-card>
                <template #reference>
                  <el-button link type="primary" size="small">
                    {{ row.couponTicketConfigs?.length || 0 }} 组规则
                  </el-button>
                </template>
              </el-popover>
            </div>
            <div v-else>
              不限影院
            </div>
          </template>
        </el-table-column>
        <el-table-column label="生成方式" align="center" prop="num" width="140">
          <template #default="{ row }">
            <span v-if="row.generateRule.generateType">一次性生成
              <el-tag type="success">{{ row.generateRule.num }} </el-tag>
              张</span>
            <span v-else>
              <el-tag type="warning">需要时生成</el-tag>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="券绑定用户数" align="center" prop="bindCount" width="110" />
        <el-table-column label="券已使用数" align="center" prop="useCount" width="90" />
        <el-table-column label="有效日期" align="center" width="180">
          <template #default="{ row }">
            <span v-if="row.periodRule.validScope">
              绑定后 <el-tag type="success">{{ row.periodRule.overdueDay }}</el-tag> 天内有效
            </span>

            <span v-else>{{ formatTimestamp(row.periodRule.startTime) }} 至 {{ formatTimestamp(row.periodRule.endTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="使用状态" align="center" width="180">
          <template #default="{ row }">
            <!-- {{ row.couponCodeCreateConfig.useStatus }} -->
            <el-switch
              v-model="row.useStatus" size="large" inline-prompt active-text="开启" inactive-text="关闭"
              :active-value="1" :inactive-value="0" @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="160" fixed="right" class-name="small-padding fixed-width">
          <template #default="{ row }">
            <div class="flex">
              <el-button type="primary" link size="small" @click="handleDetail(row, 'view')">
                详情
              </el-button>
              <el-button type="primary" class="marr8" link size="small" @click="handleShowCouponCode(row)">
                查看券码
              </el-button>
              <!-- <el-popconfirm
                title="确认复制优惠券？"
                @confirm="handleCopy(row)">
                <template #reference>
                    <el-button type="primary" link>复制</el-button>
                </template>
    </el-popconfirm> -->
              <el-dropdown trigger="click">
                <el-button type="primary" link size="small">
                  更多
                  <!-- <el-icon class="el-icon--right">
                    <arrow-down />
                  </el-icon> -->
                  <FaIcon name="arrow-down" />
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item icon="CopyDocument" @click="handleCopy(row)">
                      <FaIcon name="copy" />
                      复制
                    </el-dropdown-item>
                    <!-- <el-dropdown-item v-if="row.useStatus.code == '0'" @click="handleDetail(row, 'edit')"
                    icon="Edit">修改</el-dropdown-item> -->
                    <!-- 编辑主信息 -->
                    <el-dropdown-item style="color: #2ecc71;" @click="handleEditBase(row, 'edit')">
                      <FaIcon name="edit" />
                      修改主信息
                    </el-dropdown-item>
                    <!-- 编辑从配置 -->
                    <el-dropdown-item style="color: #9b59b6;" @click="handleEditConfig(row, 'edit')">
                      <FaIcon name="edit" />
                      修改条件
                    </el-dropdown-item>
                    <el-dropdown-item style="color: red;" @click="handleDelete(row)">
                      <FaIcon name="delete" />
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- <pagination v-show="total > 0" v-model:page="queryParams.page" v-model:limit="queryParams.pageSize" :total="total"
        @pagination="getList" />
    </fa-page-main> -->
      <el-pagination
        v-model:current-page="queryParams.page" v-model:page-size="queryParams.size" :total="total"
        :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" class="mt-4 text-right"
        @size-change="handleSizeChange" @current-change="handleCurrentChange"
      />
      </fa-page-main>

      <!-- 新增、修改券 -->
      <add-coupon-com
        v-if="showCouponDialog"
        :model-value="showCouponDialog"
        :edit-coupon="editCouponData"
        @update:model-value="showCouponDialog = $event"
        @refresh="fetchCouponList"
        @close="showCouponDialog = false"
      />

      <!-- 券详情 -->
      <coupon-detail-com
        v-if="showCouponDetail"
        :coupon-data="currentCouponData"
        @close="showCouponDetail = false"
      />

      <!-- 券码 -->
      <!-- <coupon-code
      v-if="showCouponCode" :coupon-data="couponData" :model-value="showCouponCode"
      @update:model-value="showCouponCode = $event" @refresh="getList" @close="showCouponCode = false"
    /> -->

      <!-- 券码查询结果 -->
      <!-- <coupon-code-query
      v-if="showCouponCodeQuery" :model-value="showCouponCodeQuery"
      :channel-list="channelList" @close="showCouponCodeQuery = false"
    /> -->
    </add-coupon-com>
  </div>
</template>

<style scoped lang="scss">
.flex {
  display: flex;
}

.marr8 {
  margin-right: 8px;
}
</style>
